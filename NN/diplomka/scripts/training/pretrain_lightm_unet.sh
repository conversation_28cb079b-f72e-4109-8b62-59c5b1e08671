#!/bin/bash
# Pretrain script for LightM-UNet on SpheroMix dataset
# LightM-UNet: ~1M parameters lightweight architecture

# Set CUDA device
export CUDA_VISIBLE_DEVICES=0

# Dataset path (SpheroMix - mixed quality for pretraining)
DATASET_PATH="/data/prusek/SpheroMix"

# Output directory with timestamp
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
OUTPUT_DIR="outputs/lightm_unet_pretrain_${TIMESTAMP}"

# Create output directory
mkdir -p ${OUTPUT_DIR}

# Training parameters optimized for LightM-UNet (lightweight model)
python src/training/CNN_main_spheroid.py \
    --dataset_path ${DATASET_PATH} \
    --output_dir ${OUTPUT_DIR} \
    --model lightm_unet \
    --epochs 150 \
    --batch_size 8 \
    --lr 1e-3 \
    --weight_decay 1e-5 \
    --img_size 512 \
    --focal_weight 1.0 \
    --dice_weight 1.0 \
    --iou_weight 0.5 \
    --boundary_weight 0.0 \
    --optimizer adamw \
    --scheduler cosine \
    --num_workers 8 \
    --patience 25 \
    --use_instance_norm \
    --find_lr \
    --min_delta 1e-4 \
    --gradient_accumulation_steps 1 \
    --gradient_clip_val 1.0 \
    --use_cache \
    2>&1 | tee ${OUTPUT_DIR}/training.log

echo "Pretraining completed. Model saved in ${OUTPUT_DIR}"
echo "Best model checkpoint: ${OUTPUT_DIR}/best_model.pth"