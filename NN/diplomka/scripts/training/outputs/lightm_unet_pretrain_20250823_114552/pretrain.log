=== LightM-UNet Pretraining Configuration ===
Model: lightm_unet
Dataset: /data/prusek/training_big
Output: ./outputs/lightm_unet_pretrain_20250823_114552
Batch size: 8
Image size: 1024
Epochs: 150
Learning rate: 1e-4
Weight decay: 1e-5
Optimizer: adamw
Scheduler: cosine
GPUs: 2
Workers: 8
Patience: 25
=============================================
Starting pretraining...
[rank1]:[W823 11:46:00.222029972 ProcessGroupNCCL.cpp:1479] Warning: WARNING: destroy_process_group() was not called before program exit, which can leak resources. For more info, please see https://pytorch.org/docs/stable/distributed.html#shutdown (function operator())
W0823 11:46:00.637823 3425851 torch/multiprocessing/spawn.py:169] Terminating process 3426013 via signal SIGTERM
Traceback (most recent call last):
  File "/home/<USER>/SpheroSeg/NN/diplomka/scripts/training/../../CNN_main_spheroid.py", line 1486, in <module>
    main()
  File "/home/<USER>/SpheroSeg/NN/diplomka/scripts/training/../../CNN_main_spheroid.py", line 1481, in main
    mp.spawn(train, args=(world_size, args), nprocs=world_size, join=True)
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/multiprocessing/spawn.py", line 340, in spawn
    return start_processes(fn, args, nprocs, join, daemon, start_method="spawn")
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/multiprocessing/spawn.py", line 296, in start_processes
    while not context.join():
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/multiprocessing/spawn.py", line 215, in join
    raise ProcessRaisedException(msg, error_index, failed_process.pid)
torch.multiprocessing.spawn.ProcessRaisedException: 

-- Process 1 terminated with the following error:
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/multiprocessing/spawn.py", line 90, in _wrap
    fn(i, *args)
  File "/home/<USER>/SpheroSeg/NN/diplomka/CNN_main_spheroid.py", line 1034, in train
    setup_distributed(rank, world_size)
  File "/home/<USER>/SpheroSeg/NN/diplomka/CNN_main_spheroid.py", line 721, in setup_distributed
    torch.cuda.set_device(rank)
  File "/home/<USER>/.local/lib/python3.9/site-packages/torch/cuda/__init__.py", line 529, in set_device
    torch._C._cuda_setDevice(device)
RuntimeError: CUDA error: invalid device ordinal
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.


=== Pretraining Completed Successfully ===
Model saved in: ./outputs/lightm_unet_pretrain_20250823_114552
Best model checkpoint: ./outputs/lightm_unet_pretrain_20250823_114552/best_model.pth
Training log: ./outputs/lightm_unet_pretrain_20250823_114552/pretrain.log
=========================================
