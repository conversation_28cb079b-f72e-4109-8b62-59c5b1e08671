#!/bin/bash
# Finetune script for LightM-UNet on SpheroHQ dataset
# LightM-UNet: ~1M parameters lightweight architecture

# Set CUDA device
export CUDA_VISIBLE_DEVICES=0

# Dataset path (SpheroHQ - high quality for finetuning)
DATASET_PATH="/data/prusek/SpheroHQ"

# Pretrained model path (update this to your pretrained model)
PRETRAINED_PATH="outputs/lightm_unet_pretrain_XXXXXXXX_XXXXXX/best_model.pth"

# Output directory with timestamp
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
OUTPUT_DIR="outputs/lightm_unet_finetune_${TIMESTAMP}"

# Create output directory
mkdir -p ${OUTPUT_DIR}

# Check if pretrained model exists
if [ ! -f "${PRETRAINED_PATH}" ]; then
    echo "Error: Pretrained model not found at ${PRETRAINED_PATH}"
    echo "Please update PRETRAINED_PATH to point to your pretrained model"
    exit 1
fi

# Finetuning parameters optimized for LightM-UNet
# Lower learning rate and fewer epochs for finetuning
python src/training/CNN_main_spheroid.py \
    --dataset_path ${DATASET_PATH} \
    --output_dir ${OUTPUT_DIR} \
    --model lightm_unet \
    --pretrained_path ${PRETRAINED_PATH} \
    --freeze_backbone_epochs 10 \
    --epochs 75 \
    --batch_size 8 \
    --lr 1e-5 \
    --weight_decay 1e-5 \
    --img_size 512 \
    --focal_weight 1.0 \
    --dice_weight 1.0 \
    --iou_weight 0.5 \
    --boundary_weight 0.1 \
    --optimizer adamw \
    --scheduler cosine \
    --num_workers 8 \
    --patience 20 \
    --use_instance_norm \
    --min_delta 5e-5 \
    --gradient_accumulation_steps 1 \
    --gradient_clip_val 1.0 \
    --use_tta \
    --use_cache \
    2>&1 | tee ${OUTPUT_DIR}/finetuning.log

echo "Finetuning completed. Model saved in ${OUTPUT_DIR}"
echo "Best finetuned model: ${OUTPUT_DIR}/best_model.pth"